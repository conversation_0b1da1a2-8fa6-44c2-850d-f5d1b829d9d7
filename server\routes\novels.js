const express = require("express");
const router = express.Router();
const novelController = require("../controllers/novelController");
const commentController = require("../controllers/commentController");
const ratingController = require("../controllers/ratingController");
const auth = require("../middleware/auth");

// 获取所有小说
router.get("/", novelController.getAllNovels);

// 获取单个小说
router.get("/:id", novelController.getNovelById);

// 获取当前用户的小说 (需要认证)
router.get("/user/me", auth, novelController.getUserNovels);

// 创建小说 (需要认证)
router.post("/", auth, novelController.createNovel);

// 更新小说 (需要认证)
router.put("/:id", auth, novelController.updateNovel);

// 删除小说 (需要认证)
router.delete("/:id", auth, novelController.deleteNovel);

// 获取小说的评论
router.get("/:novelId/comments", commentController.getNovelComments);

// 添加评论 (需要认证)
router.post("/:novelId/comments", auth, commentController.addComment);

// 删除评论 (需要认证)
router.delete(
  "/:novelId/comments/:commentId",
  auth,
  commentController.deleteComment
);

// 获取小说的评价统计
router.get("/:novelId/ratings/stats", ratingController.getNovelRatingStats);

// 获取用户对小说的评价 (需要认证)
router.get("/:novelId/ratings/user", auth, ratingController.getUserRating);

// 创建或更新评价 (需要认证)
router.post("/:novelId/ratings", auth, ratingController.upsertRating);

// 删除评价 (需要认证)
router.delete("/:novelId/ratings", auth, ratingController.deleteRating);

// 获取小说的详细评价信息
router.get("/:novelId/ratings", ratingController.getNovelRatingsWithUsers);

module.exports = router;
