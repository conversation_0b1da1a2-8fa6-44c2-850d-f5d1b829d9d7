const jwt = require("jsonwebtoken");
require("dotenv").config();

function auth(req, res, next) {
  // 从请求头获取token
  const token = req.header("x-auth-token");

  // 检查是否有token
  if (!token) {
    return res.status(401).json({ msg: "没有提供令牌，授权失败" });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 将用户信息添加到请求对象
    req.user = decoded;
    next();
  } catch (err) {
    res.status(401).json({ msg: "令牌无效" });
  }
}

module.exports = auth;
