import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "bootstrap/dist/css/bootstrap.min.css";
import "./App.css";

// 导入页面组件
import Home from "./pages/Home";
import Login from "./pages/Login";
import Register from "./pages/Register";
import NovelEditor from "./pages/NovelEditor";
import NovelList from "./pages/NovelList";
import NovelDetail from "./pages/NovelDetail";

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/editor" element={<NovelEditor />} />
          <Route path="/editor/:id" element={<NovelEditor />} />
          <Route path="/novels" element={<NovelList />} />
          <Route path="/novel/:id" element={<NovelDetail />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
