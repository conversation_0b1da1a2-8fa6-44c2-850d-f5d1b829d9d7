const Novel = require("../models/Novel");

// 获取所有小说
exports.getAllNovels = async (req, res) => {
  try {
    const novels = await Novel.findAll();
    res.json(novels);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 获取单个小说详情
exports.getNovelById = async (req, res) => {
  try {
    const novel = await Novel.findById(req.params.id);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }
    res.json(novel);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 获取当前用户的所有小说
exports.getUserNovels = async (req, res) => {
  try {
    const novels = await Novel.findByUserId(req.user.id);
    res.json(novels);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 创建小说
exports.createNovel = async (req, res) => {
  const { title, content } = req.body;

  // 简单验证
  if (!title || !content) {
    return res.status(400).json({ msg: "请提供标题和内容" });
  }

  try {
    const novelId = await Novel.create({
      title,
      content,
      user_id: req.user.id,
    });

    res.status(201).json({
      id: novelId,
      title,
      content,
      user_id: req.user.id,
      author: req.user.username,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 更新小说
exports.updateNovel = async (req, res) => {
  const { title, content } = req.body;

  // 简单验证
  if (!title || !content) {
    return res.status(400).json({ msg: "请提供标题和内容" });
  }

  try {
    // 检查小说是否存在且属于当前用户
    const novel = await Novel.findById(req.params.id);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }

    if (novel.user_id !== req.user.id && !req.user.is_admin) {
      return res.status(403).json({ msg: "无权限修改此小说" });
    }

    const success = await Novel.update(req.params.id, {
      title,
      content,
      user_id: req.user.id,
    });

    if (!success) {
      return res.status(404).json({ msg: "更新失败" });
    }

    res.json({ msg: "小说更新成功" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 删除小说
exports.deleteNovel = async (req, res) => {
  try {
    const success = await Novel.delete(
      req.params.id,
      req.user.id,
      req.user.is_admin
    );

    if (!success) {
      return res.status(404).json({ msg: "小说不存在或无权限删除" });
    }

    res.json({ msg: "小说删除成功" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};
