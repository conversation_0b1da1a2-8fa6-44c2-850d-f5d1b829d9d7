const Rating = require("../models/Rating");
const Novel = require("../models/Novel");

// 获取小说的评价统计
exports.getNovelRatingStats = async (req, res) => {
  try {
    const { novelId } = req.params;
    
    // 检查小说是否存在
    const novel = await Novel.findById(novelId);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }

    const stats = await Rating.getNovelRatingStats(novelId);
    res.json(stats);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 获取用户对小说的评价
exports.getUserRating = async (req, res) => {
  try {
    const { novelId } = req.params;
    const userId = req.user.id;

    const rating = await Rating.getUserRating(userId, novelId);
    res.json({ rating });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 创建或更新评价
exports.upsertRating = async (req, res) => {
  try {
    const { novelId } = req.params;
    const { ratingType } = req.body;
    const userId = req.user.id;

    // 验证评价类型
    const validRatingTypes = ['like', 'dislike', 'hate', 'super_like'];
    if (!validRatingTypes.includes(ratingType)) {
      return res.status(400).json({ msg: "无效的评价类型" });
    }

    // 检查小说是否存在
    const novel = await Novel.findById(novelId);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }

    // 检查用户是否是作者（作者不能给自己的作品评价）
    if (novel.user_id === userId) {
      return res.status(403).json({ msg: "不能给自己的作品评价" });
    }

    const success = await Rating.upsertRating(userId, novelId, ratingType);
    if (success) {
      // 返回更新后的统计数据
      const stats = await Rating.getNovelRatingStats(novelId);
      res.json({ 
        msg: "评价成功",
        stats,
        userRating: ratingType
      });
    } else {
      res.status(500).json({ msg: "评价失败" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 删除评价
exports.deleteRating = async (req, res) => {
  try {
    const { novelId } = req.params;
    const userId = req.user.id;

    const success = await Rating.deleteRating(userId, novelId);
    if (success) {
      // 返回更新后的统计数据
      const stats = await Rating.getNovelRatingStats(novelId);
      res.json({ 
        msg: "评价已删除",
        stats,
        userRating: null
      });
    } else {
      res.status(404).json({ msg: "评价不存在" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 获取小说的详细评价信息
exports.getNovelRatingsWithUsers = async (req, res) => {
  try {
    const { novelId } = req.params;
    
    // 检查小说是否存在
    const novel = await Novel.findById(novelId);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }

    const ratings = await Rating.getNovelRatingsWithUsers(novelId);
    res.json(ratings);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};
