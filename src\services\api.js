import axios from "axios";

const API_URL = "http://localhost:5000/api";

// 创建axios实例
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器，添加token到请求头
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["x-auth-token"] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 用户相关API
export const userAPI = {
  register: (userData) => api.post("/users/register", userData),
  login: (credentials) => api.post("/users/login", credentials),
  getCurrentUser: () => api.get("/users/me"),
};

// 小说相关API
export const novelAPI = {
  getAllNovels: () => api.get("/novels"),
  getNovelById: (id) => api.get(`/novels/${id}`),
  getUserNovels: () => api.get("/novels/user/me"),
  createNovel: (novelData) => api.post("/novels", novelData),
  updateNovel: (id, novelData) => api.put(`/novels/${id}`, novelData),
  deleteNovel: (id) => api.delete(`/novels/${id}`),
  getNovelComments: (novelId) => api.get(`/novels/${novelId}/comments`),
  addComment: (novelId, commentData) =>
    api.post(`/novels/${novelId}/comments`, commentData),
  deleteComment: (novelId, commentId) =>
    api.delete(`/novels/${novelId}/comments/${commentId}`),
};

// 评价相关API
export const ratingAPI = {
  getNovelRatingStats: (novelId) => api.get(`/novels/${novelId}/ratings/stats`),
  getUserRating: (novelId) => api.get(`/novels/${novelId}/ratings/user`),
  upsertRating: (novelId, ratingData) =>
    api.post(`/novels/${novelId}/ratings`, ratingData),
  deleteRating: (novelId) => api.delete(`/novels/${novelId}/ratings`),
  getNovelRatingsWithUsers: (novelId) => api.get(`/novels/${novelId}/ratings`),
};

export default api;
