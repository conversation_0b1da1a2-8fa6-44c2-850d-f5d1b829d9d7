const db = require("../config/db");

class Novel {
  static async findAll() {
    try {
      const [rows] = await db.query(`
        SELECT novels.*, users.username as author
        FROM novels
        JOIN users ON novels.user_id = users.id
        ORDER BY novels.created_at DESC
      `);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async findById(id) {
    try {
      const [rows] = await db.query(
        `
        SELECT novels.*, users.username as author
        FROM novels
        JOIN users ON novels.user_id = users.id
        WHERE novels.id = ?
      `,
        [id]
      );
      return rows[0];
    } catch (error) {
      throw error;
    }
  }

  static async findByUserId(userId) {
    try {
      const [rows] = await db.query(
        `
        SELECT novels.*, users.username as author
        FROM novels
        JOIN users ON novels.user_id = users.id
        WHERE novels.user_id = ?
        ORDER BY novels.created_at DESC
      `,
        [userId]
      );
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async create(novelData) {
    try {
      const [result] = await db.query(
        "INSERT INTO novels (title, content, user_id) VALUES (?, ?, ?)",
        [novelData.title, novelData.content, novelData.user_id]
      );
      return result.insertId;
    } catch (error) {
      throw error;
    }
  }

  static async update(id, novelData) {
    try {
      const [result] = await db.query(
        "UPDATE novels SET title = ?, content = ? WHERE id = ? AND user_id = ?",
        [novelData.title, novelData.content, id, novelData.user_id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  static async delete(id, userId, isAdmin = false) {
    try {
      let query = "DELETE FROM novels WHERE id = ?";
      const params = [id];

      // 如果不是管理员，则只能删除自己的小说
      if (!isAdmin) {
        query += " AND user_id = ?";
        params.push(userId);
      }

      const [result] = await db.query(query, params);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Novel;
