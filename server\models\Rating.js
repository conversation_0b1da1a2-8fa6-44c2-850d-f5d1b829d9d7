const db = require("../config/db");

class Rating {
  // 获取小说的所有评价统计
  static async getNovelRatingStats(novelId) {
    try {
      const [rows] = await db.query(
        `
        SELECT 
          rating_type,
          COUNT(*) as count
        FROM ratings
        WHERE novel_id = ?
        GROUP BY rating_type
        `,
        [novelId]
      );
      
      // 初始化统计对象
      const stats = {
        like: 0,
        dislike: 0,
        hate: 0,
        super_like: 0
      };
      
      // 填充统计数据
      rows.forEach(row => {
        stats[row.rating_type] = row.count;
      });
      
      return stats;
    } catch (error) {
      throw error;
    }
  }

  // 获取用户对特定小说的评价
  static async getUserRating(userId, novelId) {
    try {
      const [rows] = await db.query(
        "SELECT rating_type FROM ratings WHERE user_id = ? AND novel_id = ?",
        [userId, novelId]
      );
      return rows[0] ? rows[0].rating_type : null;
    } catch (error) {
      throw error;
    }
  }

  // 创建或更新评价
  static async upsertRating(userId, novelId, ratingType) {
    try {
      const [result] = await db.query(
        `
        INSERT INTO ratings (user_id, novel_id, rating_type) 
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        rating_type = VALUES(rating_type),
        updated_at = CURRENT_TIMESTAMP
        `,
        [userId, novelId, ratingType]
      );
      return result.insertId || result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // 删除评价
  static async deleteRating(userId, novelId) {
    try {
      const [result] = await db.query(
        "DELETE FROM ratings WHERE user_id = ? AND novel_id = ?",
        [userId, novelId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // 获取小说的详细评价信息（包含用户信息）
  static async getNovelRatingsWithUsers(novelId) {
    try {
      const [rows] = await db.query(
        `
        SELECT 
          ratings.*,
          users.username
        FROM ratings
        JOIN users ON ratings.user_id = users.id
        WHERE ratings.novel_id = ?
        ORDER BY ratings.updated_at DESC
        `,
        [novelId]
      );
      return rows;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Rating;
