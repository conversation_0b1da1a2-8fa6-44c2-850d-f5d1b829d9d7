const User = require("../models/User");
const jwt = require("jsonwebtoken");
require("dotenv").config();

// 注册用户
exports.register = async (req, res) => {
  const { username, email, password } = req.body;

  try {
    // 检查用户名是否已存在
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({ msg: "用户名已存在" });
    }

    // 检查邮箱是否已存在
    const existingEmail = await User.findByEmail(email);
    if (existingEmail) {
      return res.status(400).json({ msg: "邮箱已被注册" });
    }

    // 创建新用户
    const userId = await User.create({ username, email, password });

    // 生成JWT令牌
    const token = jwt.sign(
      { id: userId, username, email },
      process.env.JWT_SECRET,
      { expiresIn: "1h" }
    );

    res.status(201).json({
      token,
      user: {
        id: userId,
        username,
        email,
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 用户登录
exports.login = async (req, res) => {
  const { username, password } = req.body;

  try {
    // 查找用户
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(400).json({ msg: "用户不存在" });
    }

    // 验证密码
    const isMatch = await User.verifyPassword(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ msg: "密码错误" });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      {
        id: user.id,
        username: user.username,
        email: user.email,
        is_admin: user.is_admin,
      },
      process.env.JWT_SECRET,
      { expiresIn: "1h" }
    );

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        is_admin: user.is_admin,
      },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 获取当前用户信息
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findByUsername(req.user.username);
    if (!user) {
      return res.status(404).json({ msg: "用户不存在" });
    }

    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      is_admin: user.is_admin,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};
