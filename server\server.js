const express = require("express");
const cors = require("cors");
require("dotenv").config();

// 初始化Express应用
const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 路由
app.use("/api/users", require("./routes/users"));
app.use("/api/novels", require("./routes/novels"));

// 简单的测试路由
app.get("/", (req, res) => {
  res.send("小说创作网API正在运行");
});

// 设置端口并启动服务器
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`服务器运行在端口 ${PORT}`));
