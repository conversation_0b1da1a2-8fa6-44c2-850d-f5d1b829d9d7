const db = require("../config/db");

class Comment {
  static async findByNovelId(novelId) {
    try {
      const [rows] = await db.query(
        `
        SELECT comments.*, users.username
        FROM comments
        JOIN users ON comments.user_id = users.id
        WHERE comments.novel_id = ?
        ORDER BY comments.created_at DESC
      `,
        [novelId]
      );
      return rows;
    } catch (error) {
      throw error;
    }
  }

  static async create(commentData) {
    try {
      const [result] = await db.query(
        "INSERT INTO comments (content, user_id, novel_id) VALUES (?, ?, ?)",
        [commentData.content, commentData.user_id, commentData.novel_id]
      );
      return result.insertId;
    } catch (error) {
      throw error;
    }
  }

  static async delete(id, userId, isAdmin = false) {
    try {
      let query = "DELETE FROM comments WHERE id = ?";
      const params = [id];

      // 如果不是管理员，则只能删除自己的评论
      if (!isAdmin) {
        query += " AND user_id = ?";
        params.push(userId);
      }

      const [result] = await db.query(query, params);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Comment;
