const Comment = require("../models/Comment");
const Novel = require("../models/Novel");

// 获取小说的所有评论
exports.getNovelComments = async (req, res) => {
  try {
    // 检查小说是否存在
    const novel = await Novel.findById(req.params.novelId);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }

    const comments = await Comment.findByNovelId(req.params.novelId);
    res.json(comments);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 添加评论
exports.addComment = async (req, res) => {
  const { content } = req.body;
  const novelId = req.params.novelId;

  // 简单验证
  if (!content) {
    return res.status(400).json({ msg: "请提供评论内容" });
  }

  try {
    // 检查小说是否存在
    const novel = await Novel.findById(novelId);
    if (!novel) {
      return res.status(404).json({ msg: "小说不存在" });
    }

    // 所有登录用户都可以评论

    const commentId = await Comment.create({
      content,
      user_id: req.user.id,
      novel_id: novelId,
    });

    res.status(201).json({
      id: commentId,
      content,
      user_id: req.user.id,
      novel_id: novelId,
      username: req.user.username,
      created_at: new Date(),
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};

// 删除评论
exports.deleteComment = async (req, res) => {
  try {
    const success = await Comment.delete(
      req.params.commentId,
      req.user.id,
      req.user.is_admin
    );

    if (!success) {
      return res.status(404).json({ msg: "评论不存在或无权限删除" });
    }

    res.json({ msg: "评论删除成功" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: "服务器错误" });
  }
};
